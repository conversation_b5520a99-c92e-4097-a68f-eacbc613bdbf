import {
  Button,
  Container,
  Drawer,
  Separator,
  Typography,
} from '@ghq-abi/design-system-v2';
import { useMutation } from '@tanstack/react-query';
import { Form, Formik, useFormik } from 'formik';

import { FormikInput } from '~/shared/components/FormikInput';
import { FormikTextarea } from '~/shared/components/FormikTextarea';
import { SimpleDeliverableCard } from '~/shared/components/SimpleDeliverableCard';
import proposalService from '~/shared/services/proposal';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import {
  CreateTargetBody,
  CreateTargetDrawerProps,
  FormValues,
} from '../../types';

export const CreateTargetDrawer = ({
  proposalId,
  isOpen,
  onClose,
  deliverable,
  onSuccessSubmit,
}: CreateTargetDrawerProps) => {
  const { handleSubmit, initialValues, handleChange } = useFormik<FormValues>({
    initialValues: {
      definition: deliverable?.definition || '',
      calculationMethod: deliverable?.calculationMethod || '',
      weight: '',
      scope: '',
    },
    onSubmit: async values => {
      const createTargetBody: CreateTargetBody = {
        targets: [
          {
            weight: Number(values.weight),
            scope: values.scope,
            targetType: TargetTypeEnum.PROPOSAL,
            deliverable: deliverable,
            children: [
              {
                weight: Number(values.weight),
                scope: values.scope,
                uidDeliverable: deliverable?.uid || '',
              },
            ],
          },
        ],
      };
      mutate(createTargetBody);
    },
  });

  const { isLoading, mutate } = useMutation({
    mutationFn: (values: object) => {
      return proposalService.createTarget(proposalId, {
        ...values,
      });
    },
    onSuccess: (proposal: Proposal) => {
      onSuccessSubmit(proposal);
      onClose();
    },
    onError: error => {
      console.error(error);
    },
  });

  return (
    <Drawer.Root direction="right" open={isOpen} onOpenChange={onClose}>
      <Drawer.Content className="w-full max-w-lg pt-12 h-[calc(100%-74px)]">
        <Formik<FormValues>
          onSubmit={() => handleSubmit()}
          initialValues={initialValues}
        >
          {({ submitForm }) => (
            <>
              <Container className="overflow-y-auto h-full">
                <Container className="flex justify-between items-center p-4">
                  <Container className="flex flex-col">
                    <Typography variant="body-sm-bold">
                      Create Target
                    </Typography>
                  </Container>
                </Container>
                <Separator />
                <Container className="flex flex-col py-4 px-2 gap-4">
                  <SimpleDeliverableCard
                    name={deliverable?.name || 'N/A'}
                    businessFunction={deliverable?.businessFunction || 'N/A'}
                    usage={deliverable?.usage || 0}
                    deliverableType={deliverable?.type}
                  />
                  <Form>
                    <FormikTextarea
                      label="Definition"
                      name="definition"
                      value={initialValues.definition}
                      onChange={handleChange}
                      placeholder="Insert here..."
                      disabled
                    />
                    <FormikTextarea
                      label="Calculation Method"
                      name="calculationMethod"
                      value={initialValues.calculationMethod}
                      onChange={handleChange}
                      placeholder="Insert here..."
                      disabled
                    />
                    <FormikInput
                      label="Weight"
                      name="weight"
                      onChange={handleChange}
                      placeholder="Insert here..."
                    />
                    <FormikTextarea
                      label="Scope"
                      name="scope"
                      onChange={handleChange}
                      placeholder="Insert here..."
                    />
                  </Form>
                </Container>
              </Container>
              <Separator />
              <Container className="flex flex-row justify-end p-4 gap-4 w-full">
                <Button
                  variant="secondary"
                  border="default"
                  disabled={isLoading}
                  onClick={onClose}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  disabled={isLoading}
                  onClick={submitForm}
                >
                  Assign
                </Button>
              </Container>
            </>
          )}
        </Formik>
      </Drawer.Content>
    </Drawer.Root>
  );
};
