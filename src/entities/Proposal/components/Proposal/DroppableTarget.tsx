import React from 'react';
import { useDroppable } from '@dnd-kit/core';

import { Target } from '~/shared/types/Target';

export interface DroppableTargetProps {
  children: React.ReactNode;
  target: Target;
  isDragging?: boolean;
}

export function DroppableTarget({
  children,
  target,
  isDragging,
}: DroppableTargetProps) {
  const { setNodeRef, isOver } = useDroppable({
    id: `target-${target.uid}`,
    data: {
      target,
    },
  });

  return (
    <div
      ref={setNodeRef}
      style={{
        display: isDragging ? 'none' : 'block',
        backgroundColor: isOver ? '#f0f0f0' : undefined,
        border: isOver ? '2px dashed #007bff' : undefined,
      }}
    >
      {children}
    </div>
  );
}
