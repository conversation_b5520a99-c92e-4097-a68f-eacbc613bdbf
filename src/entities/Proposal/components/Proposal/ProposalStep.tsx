import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { ChildCard, TargetCard } from '~/shared/components/TargetCard';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { TabEmptyState } from '../TabEmptyState';

import { ProposalDrag } from './ProposalDrag';

interface ProposalStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  userPermission?: 'SPOC' | 'EMPLOYEE';
}

export function ProposalStep({
  targets,
  proposalUid,
  proposalStatus,
  userPermission,
}: ProposalStepProps) {
  const proposalTargets = targets.filter(target =>
    target.targetTypes?.some(
      targetType => targetType.type === TargetTypeEnum.PROPOSAL,
    ),
  );

  if (proposalTargets.length === 0 && userPermission === 'EMPLOYEE') {
    return (
      <TabEmptyState
        title="No proposals available"
        description="There are no proposal targets to display at this time."
      />
    );
  }

  return (
    <>
      {(proposalStatus !== ProposalStatusEnum.NOT_STARTED &&
        proposalStatus !== ProposalStatusEnum.IN_PROGRESS_PROPOSAL) ||
      userPermission !== 'SPOC' ? (
        <Container className="flex flex-col gap-4">
          {proposalTargets.map(target => {
            if (target.children && target.children.length > 1) {
              return (
                <TargetCard
                  key={target.uid}
                  data={target}
                  proposalStatus={proposalStatus}
                  hideChildren={target.children.length <= 1}
                  currentTargetType={TargetTypeEnum.PROPOSAL}
                />
              );
            }
            return (
              <ChildCard
                key={target.uid}
                target={target}
                disableDrag={true}
                // deleteEvent={() => handleTargetRemove(`${target.uid}`)}
              />
            );
          })}
        </Container>
      ) : (
        <ProposalDrag
          proposalStatus={proposalStatus}
          proposalUid={proposalUid}
          targets={proposalTargets}
        />
      )}
    </>
  );
}
