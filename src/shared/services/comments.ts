import axios from 'axios';

const BASE_URL = process.env.NEXT_PUBLIC_API_URL;

export interface CreateTargetCommentDto {
  comment: string;
  createdBy: string;
}

export interface CommentResponse {
  id: string;
  comment: string;
  createdBy: string;
  createdAt: string;
  author?: {
    name: string;
    globalId: string;
  };
}

export interface CommentsListResponse {
  data: CommentResponse[];
  pageNumber?: number;
  pageSize?: number;
  totalRecords?: number;
}

class CommentsService {
  async createComment(
    proposalId: string,
    commentData: CreateTargetCommentDto,
  ): Promise<CommentResponse> {
    const response = await axios.post<CommentResponse>(
      `${BASE_URL}/proposals/${proposalId}`,
      commentData,
    );
    return response.data;
  }

  async getComments(proposalId: string): Promise<CommentResponse[]> {
    const response = await axios.get<CommentsListResponse>(
      `${BASE_URL}/proposals/${proposalId}/comments`,
    );
    return response.data?.data ?? [];
  }

  async deleteComment(proposalId: string, commentId: string): Promise<void> {
    await axios.delete(
      `${BASE_URL}/proposals/${proposalId}/comments/${commentId}`,
    );
  }
}

const commentsService = new CommentsService();
export default commentsService;
