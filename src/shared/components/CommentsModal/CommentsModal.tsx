import React, {
  type ChangeEvent,
  forwardRef,
  useImperative<PERSON><PERSON>le,
  useState,
} from 'react';
import { SendFill, Trash, XLg } from 'react-bootstrap-icons';
import {
  Button,
  Drawer,
  Input,
  Separator,
  Typography,
} from '@ghq-abi/design-system-v2';
import { format } from 'date-fns';

import { Avatar } from '~/shared/components/Avatar';
import { Comment } from '~/shared/components/icons/Comment';
import { useUser } from '~/shared/hooks/useUser';
import commentsService, {
  type CommentResponse,
} from '~/shared/services/comments';

import {
  Author,
  Body,
  Item,
  ItemActions,
  ItemHeader,
  List,
  Timestamp,
} from './styles';
import type { CommentsModalProps, CommentsModalRefApi } from './types';

export const CommentsModal = forwardRef<
  CommentsModalRefApi,
  CommentsModalProps
>(({ type, parentId, title = 'Comments', onClose }, ref) => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [items, setItems] = useState<CommentResponse[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { name, globalId } = useUser();

  const refresh = async () => {
    if (type === 'catchball' && parentId) {
      try {
        setIsLoading(true);
        const comments = await commentsService.getComments(parentId);
        setItems(comments);
      } catch (error) {
        console.error('Failed to fetch comments:', error);
      } finally {
        setIsLoading(false);
      }
    }
  };

  const loadFromApi = async (fn: any) => {
    try {
      const data = await fn(type, parentId);
      setItems(data);
    } catch (error) {
      console.error('Failed to load from API:', error);
    }
  };

  useImperativeHandle(ref, () => ({
    open: () => {
      void refresh();
      setIsOpen(true);
    },
    close: () => setIsOpen(false),
    refresh,
    loadFromApi,
  }));

  const handleAdd = async () => {
    if (!message.trim() || !globalId || type !== 'catchball') {
      return;
    }

    try {
      setIsLoading(true);
      await commentsService.createComment(parentId, {
        comment: message.trim(),
        createdBy: globalId,
      });
      setMessage('');
      await refresh();
    } catch (error) {
      console.error('Failed to create comment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemove = async (id: string) => {
    if (type !== 'catchball') {
      return;
    }

    try {
      setIsLoading(true);
      await commentsService.deleteComment(parentId, id);
      await refresh();
    } catch (error) {
      console.error('Failed to delete comment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Drawer.Root
      direction="right"
      open={isOpen}
      onOpenChange={open =>
        open ? setIsOpen(true) : (setIsOpen(false), onClose?.())
      }
    >
      <Drawer.Content className="w-full max-w-xl pt-12 h-screen flex flex-col">
        <Drawer.Title hidden />
        <Drawer.Description hidden />

        <div className="px-6 pb-3 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Comment size={18} />
            <Typography variant="title-md-regular" className="font-semibold">
              {title}
            </Typography>
            <Typography variant="body-sm-regular" color="light">
              ({items.length})
            </Typography>
          </div>
          <Button
            variant="tertiary"
            onClick={() => (setIsOpen(false), onClose?.())}
            aria-label="Close comments"
          >
            <XLg />
          </Button>
        </div>

        <Separator />

        <div className="px-6 py-4 overflow-y-auto flex-1">
          <List>
            {items.length === 0 ? (
              <Typography variant="body-sm-regular" color="light">
                No comments yet
              </Typography>
            ) : (
              items.map((item: CommentResponse) => (
                <div key={item.id}>
                  <Item>
                    <ItemHeader>
                      <div className="flex items-center gap-3">
                        <Avatar
                          name={item.author?.name || 'Anonymous'}
                          globalId={item.author?.globalId ?? globalId ?? ''}
                          size={32}
                          fontScale={0.4}
                        />
                        <Author>{item.author?.name || 'Anonymous'}</Author>
                      </div>
                      <Timestamp>
                        {format(new Date(item.createdAt), 'HH:mm dd/MM')}
                      </Timestamp>
                    </ItemHeader>
                    <Body>{item.comment}</Body>
                    <ItemActions className="justify-end">
                      <Button
                        variant="tertiary"
                        onClick={() => handleRemove(item.id)}
                      >
                        <Trash />
                      </Button>
                    </ItemActions>
                  </Item>
                </div>
              ))
            )}
          </List>
        </div>

        <Drawer.Footer>
          <div className="flex items-end gap-3 p-4 w-full">
            <div className="flex-1">
              <Input
                value={message}
                onChange={(e: ChangeEvent<HTMLInputElement>) =>
                  setMessage(e.target.value)
                }
                placeholder="Type a message"
                maxLength={1000}
              />
            </div>
            <Button
              variant="tertiary"
              onClick={handleAdd}
              disabled={!message.trim()}
              aria-label="Send comment"
              className="bg-yellow-400 hover:bg-yellow-500 text-black"
            >
              <SendFill className="text-black" />
            </Button>
          </div>
        </Drawer.Footer>
      </Drawer.Content>
    </Drawer.Root>
  );
});

CommentsModal.displayName = 'CommentsModal';

export default CommentsModal;
